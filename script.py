import os
from PIL import Image

def convert_png_to_jpg(root_folder):
    for foldername, subfolders, filenames in os.walk(root_folder):
        for filename in filenames:
            if filename.lower().endswith('.png'):
                png_path = os.path.join(foldername, filename)
                jpg_path = os.path.splitext(png_path)[0] + '.jpg'
                try:
                    with Image.open(png_path) as img:
                        rgb_img = img.convert('RGB')
                        rgb_img.save(jpg_path, 'JPEG')
                    print(f"✔️ Converted: {png_path} → {jpg_path}")
                except Exception as e:
                    print(f"❌ Error converting {png_path}: {e}")

# Użycie:
convert_png_to_jpg("C:/Users/<USER>/Desktop/JPG")